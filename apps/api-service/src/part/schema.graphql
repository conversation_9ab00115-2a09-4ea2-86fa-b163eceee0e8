type Part {
  id: Int!
  z2_part_id: Int
  manufacture_id: Int!
  mpn: String!
  category: String
  subcategory0: String
  subcategory1: String
  pl_name: String
  description: String
  family_series: String
  lifecycle_status: String
  rohs_status: String
  last_z2data_update: String
  market_status: String
  number_of_seller: Int
  total_quantity_available: Float
  lowest_price: Float
  min_lead_time_weeks: Int
  max_lead_time_weeks: Int
  manufacturer: Manufacturer
  supplies: [DbSupply!]
}

type Manufacturer {
  id: Int!
  name: String!
}

type Seller {
  id: Int!
  z2_seller_id: Int!
  name: String!
  key: String!
}

type PartAlternative {
  mpn: String!
  companyName: String!
  dataSheet: String
  crossType: String!
  crossComment: String!
  partDescription: String!
  partLifecycle: String!
  roHsFlag: String
  package: String!
}

type PartAlternativeResponse {
  numFound: Int!
  pageNumber: Int!
  mpn: String!
  companyName: String!
  partLifecycle: String!
  roHsFlag: String
  dataSheet: String
  totalCrossesFound: Int!
  alternatives: [PartAlternative!]!
}

type Query {
  searchLocalParts(
    keyword: String!
    limit: Int
    forceUpdate: Boolean = false
  ): [Part!]!
  getPartAlternatives(partId: Int!): PartAlternativeResponse
}
