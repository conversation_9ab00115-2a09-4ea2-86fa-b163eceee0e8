import { Injectable, Logger } from "@nestjs/common";

import { part, manufacturer } from "../generated/client";
import { PrismaService } from "../prisma.service";
import { Z2CrossResponse, Z2CrossItem } from "../z2data/z2data.types";
import { PartAlternativeResponse } from "./types";

@Injectable()
export class PartAlternativeRepo {
  private readonly logger = new Logger(PartAlternativeRepo.name);
  private readonly cacheExpirationDays = 7;

  constructor(private prisma: PrismaService) {}

  async findCachedAlternatives(
    partId: number,
  ): Promise<PartAlternativeResponse | null> {
    try {
      const cached = await this.prisma.part_alternative_cache.findUnique({
        include: {
          alternative_items: true,
        },
        where: { part_id: partId },
      });

      if (!cached) {
        this.logger.debug(`No cached alternatives found for part ID ${partId}`);
        return null;
      }

      if (this.isCacheExpired(cached.updated_at)) {
        this.logger.debug(`Cached alternatives expired for part ID ${partId}`);
        return null;
      }

      this.logger.debug(`Returning cached alternatives for part ID ${partId}`);
      return {
        alternatives: cached.alternative_items.map((item) => ({
          companyName: item.company_name,
          crossComment: item.cross_comment,
          crossType: item.cross_type,
          dataSheet: item.data_sheet,
          mpn: item.mpn,
          package: item.package,
          partDescription: item.part_description,
          partLifecycle: item.part_lifecycle,
          roHsFlag: item.rohs_flag || undefined,
        })),
        companyName: cached.company_name,
        dataSheet: cached.data_sheet,
        mpn: cached.mpn,
        numFound: cached.num_found,
        pageNumber: cached.page_number,
        partLifecycle: cached.part_lifecycle,
        roHsFlag: cached.rohs_flag || undefined,
        totalCrossesFound: cached.total_crosses_found,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch cached alternatives for part ID ${partId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
      return null;
    }
  }

  async upsertAlternatives(
    partId: number,
    z2PartId: number,
    z2Response: Z2CrossResponse,
    alternativeItemsWithPartIds: Array<Z2CrossItem & { localPartId: number }>,
  ): Promise<void> {
    try {
      this.logger.debug(`Upserting alternatives cache for part ID ${partId}`);

      await this.prisma.$transaction(async (tx) => {
        const existingCache = await tx.part_alternative_cache.findUnique({
          where: { part_id: partId },
        });

        const itemsToCreate = alternativeItemsWithPartIds;

        if (existingCache) {
          await tx.part_alternative_items.deleteMany({
            where: { cache_id: existingCache.id },
          });

          await tx.part_alternative_cache.update({
            data: {
              company_name: z2Response.results.companyName,
              data_sheet: z2Response.results.dataSheet,
              mpn: z2Response.results.partNumber,
              num_found: z2Response.results.numFound,
              page_number: z2Response.results.pageNumber,
              part_lifecycle: z2Response.results.partLifecycle,
              rohs_flag: z2Response.results.roHsFlag,
              total_crosses_found:
                z2Response.results.crossesDetails.Total_Crosses_Found,
              updated_at: new Date(),
              z2_part_id: z2PartId,
            },
            where: { part_id: partId },
          });

          await tx.part_alternative_items.createMany({
            data: itemsToCreate.map((cross) => ({
              cache_id: existingCache.id,
              company_name: cross.companyName,
              cross_comment: cross.crossComment,
              cross_type: cross.crossType,
              data_sheet: cross.dataSheet,
              mpn: cross.partNumber,
              package: cross.package,
              part_description: cross.partDescription,
              part_lifecycle: cross.partLifecycle,
              rohs_flag: cross.roHsFlag,
              part_id: cross.localPartId,
            })),
          });
        } else {
          const newCache = await tx.part_alternative_cache.create({
            data: {
              company_name: z2Response.results.companyName,
              data_sheet: z2Response.results.dataSheet,
              mpn: z2Response.results.partNumber,
              num_found: z2Response.results.numFound,
              page_number: z2Response.results.pageNumber,
              part_id: partId,
              part_lifecycle: z2Response.results.partLifecycle,
              rohs_flag: z2Response.results.roHsFlag,
              total_crosses_found:
                z2Response.results.crossesDetails.Total_Crosses_Found,
              z2_part_id: z2PartId,
            },
          });

          await tx.part_alternative_items.createMany({
            data: itemsToCreate.map((cross) => ({
              cache_id: newCache.id,
              company_name: cross.companyName,
              cross_comment: cross.crossComment,
              cross_type: cross.crossType,
              data_sheet: cross.dataSheet,
              mpn: cross.partNumber,
              package: cross.package,
              part_description: cross.partDescription,
              part_lifecycle: cross.partLifecycle,
              rohs_flag: cross.roHsFlag,
              part_id: cross.localPartId,
            })),
          });
        }
      });

      this.logger.debug(
        `Successfully upserted alternatives cache for part ID ${partId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to upsert alternatives cache for part ID ${partId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
      throw error;
    }
  }

  async findAlternativePartLocally(
    mpn: string,
    manufacturerName: string,
  ): Promise<(part & { manufacturer: manufacturer }) | null> {
    try {
      this.logger.debug(
        `Searching for alternative part locally: MPN=${mpn}, Manufacturer=${manufacturerName}`,
      );

      return await this.prisma.part.findFirst({
        include: {
          manufacturer: true,
        },
        where: {
          mpn: {
            equals: mpn,
            mode: "insensitive",
          },
          manufacturer: {
            name: {
              equals: manufacturerName,
              mode: "insensitive",
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to search for alternative part locally: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
      return null;
    }
  }



  private isCacheExpired(updatedAt: Date): boolean {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - updatedAt.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > this.cacheExpirationDays;
  }
}
