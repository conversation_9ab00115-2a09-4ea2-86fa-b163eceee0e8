import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { firstValueFrom } from "rxjs";

import {
  Z2CrossResponse,
  z2CrossResponseSchema,
  Z2PartDetailsResponse,
  z2PartDetailsResponseSchema,
  Z2PartSearchResponse,
  z2PartSearchResponseSchema,
  Z2PartSearchResult,
  Z2ValidationRequest,
  Z2ValidationResponse,
  z2ValidationResponseSchema,
} from "./z2data.types";

@Injectable()
export class Z2DataService {
  private readonly logger = new Logger(Z2DataService.name);
  private readonly baseApiUrl = process.env.Z2DATA_BASE_API_URL;
  private readonly partValidationEndpoint = "/GetValidationPart";
  private readonly crossDataEndpoint = "/GetCrossDataByPartId";
  private readonly partSearchEndpoint = "/GetPartDetailsBySearch";
  private readonly partDetailsEndpoint = "/GetPartDetailsBypartID";
  private readonly apiKey = process.env.Z2DATA_API_KEY;

  constructor(private readonly httpService: HttpService) {}

  async validateParts(
    request: Z2ValidationRequest,
  ): Promise<Z2ValidationResponse | null> {
    try {
      this.logger.debug(
        `Validating ${request.rows.length} parts with Z2Data API`,
      );

      const response = await firstValueFrom(
        this.httpService.post<Z2ValidationResponse>(
          `${this.baseApiUrl}${this.partValidationEndpoint}?ApiKey=${this.apiKey}`,
          request,
          {
            headers: {
              "Content-Type": "application/json",
            },
          },
        ),
      );

      return z2ValidationResponseSchema.parse(response.data);
    } catch (error) {
      this.logger.error(
        `Failed to validate parts with Z2Data API: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );

      if (error instanceof Error && error.name === "ZodError") {
        throw new HttpException(
          `Invalid response from Z2Data API: ${error.message}`,
          HttpStatus.BAD_GATEWAY,
        );
      }

      return null;
    }
  }

  async getCrossDataByPartId(partId: number): Promise<Z2CrossResponse | null> {
    try {
      this.logger.debug(
        `Getting cross data for part ID ${partId} from Z2Data API`,
      );

      const response = await firstValueFrom(
        this.httpService.get<Z2CrossResponse>(
          `${this.baseApiUrl}${this.crossDataEndpoint}?ApiKey=${this.apiKey}&PartID=${partId}`,
          {
            headers: {
              "Content-Type": "application/json",
            },
          },
        ),
      );

      return z2CrossResponseSchema.parse(response.data);
    } catch (error) {
      this.logger.error(
        `Failed to get cross data for part ID ${partId} from Z2Data API: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );

      if (error instanceof Error && error.name === "ZodError") {
        throw new HttpException(
          `Invalid response from Z2Data API: ${error.message}`,
          HttpStatus.BAD_GATEWAY,
        );
      }

      return null;
    }
  }

  async searchParts(mpn: string): Promise<Z2PartSearchResult[]> {
    try {
      this.logger.debug(`Searching for parts with MPN: ${mpn}`);

      const response = await firstValueFrom(
        this.httpService.get<Z2PartSearchResponse>(
          `${this.baseApiUrl}${this.partSearchEndpoint}`,
          {
            headers: {
              "Content-Type": "application/json",
            },
            params: {
              ApiKey: this.apiKey,
              Z2MPN: mpn,
            },
          },
        ),
      );

      const parsedResponse = z2PartSearchResponseSchema.parse(response.data);
      return parsedResponse.results.PartSearch.Result;
    } catch (error) {
      this.logger.error(
        `Failed to search parts with MPN ${mpn}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );

      if (error instanceof Error && error.name === "ZodError") {
        throw new HttpException(
          `Invalid response from Z2Data API: ${error.message}`,
          HttpStatus.BAD_GATEWAY,
        );
      }

      return [];
    }
  }

  async getPartDetailsById(
    partId: number,
  ): Promise<Z2PartDetailsResponse | null> {
    try {
      this.logger.debug(
        `Getting part details for part ID ${partId} from Z2Data API`,
      );

      const response = await firstValueFrom(
        this.httpService.get<Z2PartDetailsResponse>(
          `${this.baseApiUrl}${this.partDetailsEndpoint}`,
          {
            headers: {
              "Content-Type": "application/json",
            },
            params: {
              ApiKey: this.apiKey,
              PartId: partId,
            },
          },
        ),
      );

      return z2PartDetailsResponseSchema.parse(response.data);
    } catch (error) {
      this.logger.error(
        `Failed to get part details for part ID ${partId} from Z2Data API: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );

      if (error instanceof Error && error.name === "ZodError") {
        throw new HttpException(
          `Invalid response from Z2Data API: ${error.message}`,
          HttpStatus.BAD_GATEWAY,
        );
      }

      return null;
    }
  }
}
